#!/usr/bin/env python3
"""
Analyse et prédiction des données de compteur écologique
"""

import seaborn as sns
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Configuration des graphiques
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)

class EcoCounterPredictor:
    """Classe pour l'analyse et la prédiction des données de compteur écologique"""
    
    def __init__(self, data_file):
        self.data_file = data_file
        self.df = None
        self.model = None
        self.scaler = StandardScaler()
        
    def load_and_clean_data(self):
        """Charge et nettoie les données"""
        print("📊 Chargement des données...")
        
        # Chargement des données
        self.df = pd.read_csv(self.data_file, sep=';')
        print(f"Données chargées: {len(self.df)} lignes, {len(self.df.columns)} colonnes")
        
        # Nettoyage des données
        print("🧹 Nettoyage des données...")
        
        # Nettoyage des counts (conversion en numérique)
        self.df['counts'] = pd.to_numeric(self.df['counts'], errors='coerce')

        # Suppression des lignes avec des counts manquants
        initial_len = len(self.df)
        self.df = self.df.dropna(subset=['counts'])
        print(f"Lignes supprimées (counts manquants): {initial_len - len(self.df)}")

        # Conversion des dates (après nettoyage des counts)
        print("Conversion des dates...")
        self.df['date'] = pd.to_datetime(self.df['date'], errors='coerce', utc=True)
        self.df['isoDate'] = pd.to_datetime(self.df['isoDate'], errors='coerce', utc=True)

        # Suppression des lignes avec des dates invalides
        initial_len_dates = len(self.df)
        self.df = self.df.dropna(subset=['date'])
        print(f"Lignes supprimées (dates invalides): {initial_len_dates - len(self.df)}")
        print(f"Lignes après nettoyage des dates: {len(self.df)}")

        # Vérification que la conversion a fonctionné
        if not pd.api.types.is_datetime64_any_dtype(self.df['date']):
            raise ValueError("La conversion des dates a échoué")

        # Extraction des features temporelles
        self.df['year'] = self.df['date'].dt.year
        self.df['month'] = self.df['date'].dt.month
        self.df['day'] = self.df['date'].dt.day
        self.df['hour'] = self.df['date'].dt.hour
        self.df['dayofweek'] = self.df['date'].dt.dayofweek
        self.df['dayofyear'] = self.df['date'].dt.dayofyear
        
        # Tri par date
        self.df = self.df.sort_values('date').reset_index(drop=True)
        
        print(f"✅ Données nettoyées: {len(self.df)} lignes")
        return self.df
    
    def exploratory_analysis(self):
        """Analyse exploratoire des données"""
        print("\n🔍 Analyse exploratoire des données...")
        
        # Statistiques descriptives
        print("\n📈 Statistiques descriptives:")
        print(self.df['counts'].describe())
        
        # Création des graphiques
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # Distribution des counts
        axes[0, 0].hist(self.df['counts'], bins=50, alpha=0.7, color='skyblue')
        axes[0, 0].set_title('Distribution des comptages')
        axes[0, 0].set_xlabel('Nombre de passages')
        axes[0, 0].set_ylabel('Fréquence')
        
        # Évolution temporelle
        daily_counts = self.df.groupby(self.df['date'].dt.date)['counts'].sum()
        axes[0, 1].plot(daily_counts.index, daily_counts.values, alpha=0.7)
        axes[0, 1].set_title('Évolution des comptages dans le temps')
        axes[0, 1].set_xlabel('Date')
        axes[0, 1].set_ylabel('Comptages quotidiens')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # Comptages par heure
        hourly_avg = self.df.groupby('hour')['counts'].mean()
        axes[1, 0].bar(hourly_avg.index, hourly_avg.values, alpha=0.7, color='lightgreen')
        axes[1, 0].set_title('Comptages moyens par heure')
        axes[1, 0].set_xlabel('Heure')
        axes[1, 0].set_ylabel('Comptages moyens')
        
        # Comptages par jour de la semaine
        weekday_avg = self.df.groupby('dayofweek')['counts'].mean()
        days = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim']
        axes[1, 1].bar(range(7), weekday_avg.values, alpha=0.7, color='coral')
        axes[1, 1].set_title('Comptages moyens par jour de la semaine')
        axes[1, 1].set_xlabel('Jour de la semaine')
        axes[1, 1].set_ylabel('Comptages moyens')
        axes[1, 1].set_xticks(range(7))
        axes[1, 1].set_xticklabels(days)
        
        plt.tight_layout()
        plt.savefig('exploratory_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return self.df
    
    def prepare_features(self):
        """Prépare les features pour la prédiction"""
        print("\n🔧 Préparation des features...")
        
        # Sélection des features
        feature_columns = ['year', 'month', 'day', 'hour', 'dayofweek', 'dayofyear']
        
        # Ajout de features cycliques pour capturer la saisonnalité
        self.df['hour_sin'] = np.sin(2 * np.pi * self.df['hour'] / 24)
        self.df['hour_cos'] = np.cos(2 * np.pi * self.df['hour'] / 24)
        self.df['month_sin'] = np.sin(2 * np.pi * self.df['month'] / 12)
        self.df['month_cos'] = np.cos(2 * np.pi * self.df['month'] / 12)
        self.df['dayofweek_sin'] = np.sin(2 * np.pi * self.df['dayofweek'] / 7)
        self.df['dayofweek_cos'] = np.cos(2 * np.pi * self.df['dayofweek'] / 7)
        
        # Features finales
        feature_columns.extend(['hour_sin', 'hour_cos', 'month_sin', 'month_cos', 
                               'dayofweek_sin', 'dayofweek_cos'])
        
        X = self.df[feature_columns]
        y = self.df['counts']
        
        print(f"Features sélectionnées: {feature_columns}")
        print(f"Forme des données: X={X.shape}, y={y.shape}")
        
        return X, y
    
    def train_model(self, X, y, test_size=0.2):
        """Entraîne le modèle de prédiction"""
        print(f"\n🤖 Entraînement du modèle (test_size={test_size})...")
        
        # Division des données
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=42, shuffle=False
        )
        
        # Normalisation des features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Entraînement du modèle
        self.model = LinearRegression()
        self.model.fit(X_train_scaled, y_train)
        
        # Prédictions
        y_train_pred = self.model.predict(X_train_scaled)
        y_test_pred = self.model.predict(X_test_scaled)
        
        # Évaluation
        train_mse = mean_squared_error(y_train, y_train_pred)
        test_mse = mean_squared_error(y_test, y_test_pred)
        train_r2 = r2_score(y_train, y_train_pred)
        test_r2 = r2_score(y_test, y_test_pred)
        train_mae = mean_absolute_error(y_train, y_train_pred)
        test_mae = mean_absolute_error(y_test, y_test_pred)
        
        print(f"\n📊 Résultats de l'entraînement:")
        print(f"Train MSE: {train_mse:.2f}, Test MSE: {test_mse:.2f}")
        print(f"Train R²: {train_r2:.3f}, Test R²: {test_r2:.3f}")
        print(f"Train MAE: {train_mae:.2f}, Test MAE: {test_mae:.2f}")
        
        # Visualisation des résultats
        self.plot_predictions(y_test, y_test_pred, y_train, y_train_pred)
        
        return {
            'X_train': X_train, 'X_test': X_test,
            'y_train': y_train, 'y_test': y_test,
            'y_train_pred': y_train_pred, 'y_test_pred': y_test_pred,
            'metrics': {
                'train_mse': train_mse, 'test_mse': test_mse,
                'train_r2': train_r2, 'test_r2': test_r2,
                'train_mae': train_mae, 'test_mae': test_mae
            }
        }
    
    def plot_predictions(self, y_test, y_test_pred, y_train, y_train_pred):
        """Visualise les prédictions"""
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Scatter plot des prédictions vs réalité
        axes[0].scatter(y_test, y_test_pred, alpha=0.5, label='Test')
        axes[0].scatter(y_train, y_train_pred, alpha=0.3, label='Train')
        axes[0].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
        axes[0].set_xlabel('Valeurs réelles')
        axes[0].set_ylabel('Prédictions')
        axes[0].set_title('Prédictions vs Réalité')
        axes[0].legend()
        
        # Résidus
        residuals_test = y_test - y_test_pred
        axes[1].scatter(y_test_pred, residuals_test, alpha=0.5)
        axes[1].axhline(y=0, color='r', linestyle='--')
        axes[1].set_xlabel('Prédictions')
        axes[1].set_ylabel('Résidus')
        axes[1].set_title('Analyse des résidus')
        
        plt.tight_layout()
        plt.savefig('prediction_results.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def predict_future(self, future_hours=24):
        """Prédit les comptages pour les prochaines heures"""
        if self.model is None:
            print("❌ Le modèle n'est pas encore entraîné!")
            return None
        
        print(f"\n🔮 Prédiction pour les {future_hours} prochaines heures...")
        
        # Dernière date dans les données
        last_date = self.df['date'].max()
        
        # Génération des dates futures
        future_dates = pd.date_range(
            start=last_date + pd.Timedelta(hours=1),
            periods=future_hours,
            freq='H'
        )
        
        # Création du DataFrame pour les prédictions futures
        future_df = pd.DataFrame({
            'date': future_dates,
            'year': future_dates.year,
            'month': future_dates.month,
            'day': future_dates.day,
            'hour': future_dates.hour,
            'dayofweek': future_dates.dayofweek,
            'dayofyear': future_dates.dayofyear
        })
        
        # Ajout des features cycliques
        future_df['hour_sin'] = np.sin(2 * np.pi * future_df['hour'] / 24)
        future_df['hour_cos'] = np.cos(2 * np.pi * future_df['hour'] / 24)
        future_df['month_sin'] = np.sin(2 * np.pi * future_df['month'] / 12)
        future_df['month_cos'] = np.cos(2 * np.pi * future_df['month'] / 12)
        future_df['dayofweek_sin'] = np.sin(2 * np.pi * future_df['dayofweek'] / 7)
        future_df['dayofweek_cos'] = np.cos(2 * np.pi * future_df['dayofweek'] / 7)
        
        # Sélection des features
        feature_columns = ['year', 'month', 'day', 'hour', 'dayofweek', 'dayofyear',
                          'hour_sin', 'hour_cos', 'month_sin', 'month_cos', 
                          'dayofweek_sin', 'dayofweek_cos']
        
        X_future = future_df[feature_columns]
        X_future_scaled = self.scaler.transform(X_future)
        
        # Prédictions
        future_predictions = self.model.predict(X_future_scaled)
        
        # Résultats
        results_df = pd.DataFrame({
            'date': future_dates,
            'predicted_counts': future_predictions
        })
        
        print("🎯 Prédictions futures:")
        print(results_df.head(10))
        
        # Visualisation
        plt.figure(figsize=(12, 6))
        plt.plot(results_df['date'], results_df['predicted_counts'], 
                marker='o', linewidth=2, markersize=4)
        plt.title(f'Prédictions pour les {future_hours} prochaines heures')
        plt.xlabel('Date')
        plt.ylabel('Comptages prédits')
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('future_predictions.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return results_df

def main():
    """Fonction principale"""
    print("🚀 Démarrage de l'analyse de prédiction des données écologiques")
    print("=" * 60)
    
    # Initialisation
    predictor = EcoCounterPredictor('eco-counter-data.csv')
    
    # Chargement et nettoyage des données
    df = predictor.load_and_clean_data()
    
    # Analyse exploratoire
    predictor.exploratory_analysis()
    
    # Préparation des features
    X, y = predictor.prepare_features()
    
    # Entraînement du modèle
    results = predictor.train_model(X, y)
    
    # Prédictions futures
    future_predictions = predictor.predict_future(future_hours=48)
    
    print("\n✅ Analyse terminée!")
    print("📁 Fichiers générés:")
    print("  - exploratory_analysis.png")
    print("  - prediction_results.png") 
    print("  - future_predictions.png")

if __name__ == "__main__":
    main()
