#!/usr/bin/env python3
"""
Analyse rapide des données de compteur écologique
"""

import seaborn as sns
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score

# Configuration des graphiques
plt.style.use('default')
sns.set_palette("husl")

def load_data():
    """Charge les données"""
    print("📊 Chargement des données...")
    df = pd.read_csv('eco-counter-data.csv', sep=';')
    
    # Conversion des dates et nettoyage
    df['date'] = pd.to_datetime(df['date'])
    df['counts'] = pd.to_numeric(df['counts'], errors='coerce')
    df = df.dropna(subset=['counts'])
    
    print(f"Données chargées: {len(df)} lignes")
    return df

def basic_analysis(df):
    """Analyse de base"""
    print("\n🔍 Analyse de base:")
    print(f"Période: {df['date'].min()} à {df['date'].max()}")
    print(f"Comptages - Min: {df['counts'].min()}, Max: {df['counts'].max()}, Moyenne: {df['counts'].mean():.1f}")
    
    # Graphique simple
    plt.figure(figsize=(12, 4))
    daily_counts = df.groupby(df['date'].dt.date)['counts'].sum()
    plt.plot(daily_counts.index, daily_counts.values)
    plt.title('Évolution des comptages quotidiens')
    plt.xlabel('Date')
    plt.ylabel('Comptages')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()

def simple_prediction(df):
    """Prédiction simple basée sur l'heure"""
    print("\n🤖 Prédiction simple...")
    
    # Features simples
    df['hour'] = df['date'].dt.hour
    df['dayofweek'] = df['date'].dt.dayofweek
    
    X = df[['hour', 'dayofweek']]
    y = df['counts']
    
    # Division des données
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Modèle
    model = LinearRegression()
    model.fit(X_train, y_train)
    
    # Prédictions
    y_pred = model.predict(X_test)
    
    # Métriques
    mse = mean_squared_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    
    print(f"MSE: {mse:.2f}")
    print(f"R²: {r2:.3f}")
    
    # Graphique des prédictions
    plt.figure(figsize=(10, 6))
    plt.scatter(y_test, y_pred, alpha=0.5)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
    plt.xlabel('Valeurs réelles')
    plt.ylabel('Prédictions')
    plt.title('Prédictions vs Réalité')
    plt.show()
    
    return model

def main():
    """Fonction principale"""
    print("🚀 Analyse rapide des données")
    
    # Chargement
    df = load_data()
    
    # Analyse de base
    basic_analysis(df)
    
    # Prédiction simple
    model = simple_prediction(df)
    
    print("\n✅ Analyse terminée!")

if __name__ == "__main__":
    main()
